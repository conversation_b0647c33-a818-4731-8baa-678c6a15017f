<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ConferenceMapper">

    <resultMap type="Conference" id="ConferenceResult">
        <result property="id" column="id"/>
        <result property="conferenceTitle" column="conference_title"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="address" column="address"/>
        <result property="bookingOpenTime" column="booking_open_time"/>
        <result property="bookingCloseTime" column="booking_close_time"/>
        <result property="participantCount" column="participant_count"/>
        <result property="hotelName" column="hotel_name"/>
        <result property="hotelAddress" column="hotel_address"/>
        <result property="bookingRangeStart" column="booking_range_start"/>
        <result property="bookingRangeEnd" column="booking_range_end"/>
        <result property="enable" column="enable"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="operator" column="operator"/>
    </resultMap>

    <sql id="selectConferenceVo">
        select id,
               conference_title,
               start_time,
               end_time,
               address,
               booking_open_time,
               booking_close_time,
               participant_count,
               hotel_name,
               hotel_address,
               booking_range_start,
               booking_range_end,
               enable,
               create_time,
               update_time,
               operator
        from conference
    </sql>

    <select id="selectConferenceList" parameterType="Conference" resultMap="ConferenceResult">
        <include refid="selectConferenceVo"/>
        <where>
            <if test="conferenceTitle != null  and conferenceTitle != ''">and conference_title like concat('%',
                #{conferenceTitle}, '%')
            </if>
            <if test="startTime != null ">and start_time = #{startTime}</if>
            <if test="endTime != null ">and end_time = #{endTime}</if>
            <if test="address != null  and address != ''">and address = #{address}</if>
            <if test="bookingOpenTime != null ">and booking_open_time = #{bookingOpenTime}</if>
            <if test="bookingCloseTime != null ">and booking_close_time = #{bookingCloseTime}</if>
            <if test="participantCount != null ">and participant_count = #{participantCount}</if>
            <if test="hotelName != null  and hotelName != ''">and hotel_name like concat('%', #{hotelName}, '%')</if>
            <if test="hotelAddress != null  and hotelAddress != ''">and hotel_address = #{hotelAddress}</if>
            <if test="bookingRangeStart != null ">and booking_range_start = #{bookingRangeStart}</if>
            <if test="bookingRangeEnd != null ">and booking_range_end = #{bookingRangeEnd}</if>
            <if test="enable != null and enable != ''">and enable = #{enable}</if>
            <if test="operator != null  and operator != ''">and operator = #{operator}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectConferenceById" parameterType="Long" resultMap="ConferenceResult">
        <include refid="selectConferenceVo"/>
        where id = #{id}
    </select>

    <select id="selectEnabledConferenceList" resultMap="ConferenceResult">
        <include refid="selectConferenceVo"/>
        where enable = 'Y'
        order by create_time desc
    </select>

    <insert id="insertConference" parameterType="Conference" useGeneratedKeys="true" keyProperty="id">
        insert into conference
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="conferenceTitle != null and conferenceTitle != ''">conference_title,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="address != null">address,</if>
            <if test="bookingOpenTime != null">booking_open_time,</if>
            <if test="bookingCloseTime != null">booking_close_time,</if>
            <if test="participantCount != null">participant_count,</if>
            <if test="hotelName != null">hotel_name,</if>
            <if test="hotelAddress != null">hotel_address,</if>
            <if test="bookingRangeStart != null">booking_range_start,</if>
            <if test="bookingRangeEnd != null">booking_range_end,</if>
            <if test="enable != null">enable,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="operator != null">operator,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="conferenceTitle != null and conferenceTitle != ''">#{conferenceTitle},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="address != null">#{address},</if>
            <if test="bookingOpenTime != null">#{bookingOpenTime},</if>
            <if test="bookingCloseTime != null">#{bookingCloseTime},</if>
            <if test="participantCount != null">#{participantCount},</if>
            <if test="hotelName != null">#{hotelName},</if>
            <if test="hotelAddress != null">#{hotelAddress},</if>
            <if test="bookingRangeStart != null">#{bookingRangeStart},</if>
            <if test="bookingRangeEnd != null">#{bookingRangeEnd},</if>
            <if test="enable != null">#{enable},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="operator != null">#{operator},</if>
        </trim>
    </insert>

    <update id="updateConference" parameterType="Conference">
        update conference
        <trim prefix="SET" suffixOverrides=",">
            <if test="conferenceTitle != null and conferenceTitle != ''">conference_title = #{conferenceTitle},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="address != null">address = #{address},</if>
            <if test="bookingOpenTime != null">booking_open_time = #{bookingOpenTime},</if>
            <if test="bookingCloseTime != null">booking_close_time = #{bookingCloseTime},</if>
            <if test="participantCount != null">participant_count = #{participantCount},</if>
            <if test="hotelName != null">hotel_name = #{hotelName},</if>
            <if test="hotelAddress != null">hotel_address = #{hotelAddress},</if>
            <if test="bookingRangeStart != null">booking_range_start = #{bookingRangeStart},</if>
            <if test="bookingRangeEnd != null">booking_range_end = #{bookingRangeEnd},</if>
            <if test="enable != null">enable = #{enable},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="operator != null">operator = #{operator},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteConferenceById" parameterType="Long">
        delete
        from conference
        where id = #{id}
    </delete>

    <delete id="deleteConferenceByIds" parameterType="String">
        delete from conference where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
