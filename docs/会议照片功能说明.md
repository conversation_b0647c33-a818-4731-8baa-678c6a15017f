# 会议照片功能说明

## 功能概述
为会议管理系统新增了会议照片上传功能，用户可以为每个会议上传一张照片。

## 修改内容

### 1. 数据库修改
- 在 `conference` 表中新增 `photo_path` 字段，类型为 VARCHAR(255)
- 执行 SQL: `ALTER TABLE conference ADD COLUMN photo_path VARCHAR(255) COMMENT '会议照片路径' AFTER operator;`

### 2. 后端修改
- **Conference.java**: 新增 `photoPath` 字段及其 getter/setter 方法
- **ConferenceMapper.xml**: 更新所有相关 SQL 语句以支持新字段

### 3. 前端修改
- **新增页面** (`add.html`): 添加图片上传控件
- **编辑页面** (`edit.html`): 添加图片上传控件，支持显示现有图片
- **查看页面** (`view.html`): 显示会议照片
- **列表页面** (`conference.html`): 在表格中显示缩略图

## 使用说明

### 上传图片
1. 在新增或编辑会议页面，找到"会议照片"字段
2. 点击"选择图片"按钮选择图片文件
3. 支持的图片格式：jpg, jpeg, png, gif, bmp
4. 图片会自动上传到服务器并保存路径

### 查看图片
1. 在会议列表页面，可以看到每个会议的缩略图
2. 点击缩略图可以查看大图
3. 在会议详情页面可以查看完整的会议照片

## 技术实现
- 使用 Bootstrap FileInput 插件实现文件上传
- 图片存储在服务器的 upload 目录下
- 数据库只存储图片的相对路径
- 前端通过路径拼接完整的图片 URL

## 注意事项
1. 确保服务器有足够的存储空间
2. 建议对上传的图片大小进行限制
3. 定期清理无用的图片文件
