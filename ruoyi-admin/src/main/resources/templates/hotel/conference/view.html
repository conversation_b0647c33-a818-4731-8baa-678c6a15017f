<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('会议详情')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-conference-view" th:object="${conference}">
            <div class="form-group">    
                <label class="col-sm-3 control-label">会议名称：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:field="*{conferenceTitle}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">开始时间：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:value="${#dates.format(conference.startTime, 'yyyy-MM-dd HH:mm:ss')}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">结束时间：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:value="${#dates.format(conference.endTime, 'yyyy-MM-dd HH:mm:ss')}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">会议地址：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:field="*{address}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">酒店房间开放预定时间：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:value="${#dates.format(conference.bookingOpenTime, 'yyyy-MM-dd HH:mm:ss')}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">酒店房间关闭预定时间：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:value="${#dates.format(conference.bookingCloseTime, 'yyyy-MM-dd HH:mm:ss')}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">参会人员数量：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:field="*{participantCount}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">酒店名称：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:field="*{hotelName}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">酒店地址：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:field="*{hotelAddress}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">房间预定区间起始：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:value="${#dates.format(conference.bookingRangeStart, 'yyyy-MM-dd')}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">房间预定时间终止：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:value="${#dates.format(conference.bookingRangeEnd, 'yyyy-MM-dd')}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">是否启用：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:value="${@dict.getLabel('sys_yes_no', conference.enable)}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">操作人：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:field="*{operator}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">创建时间：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:value="${#dates.format(conference.createTime, 'yyyy-MM-dd HH:mm:ss')}" readonly>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">更新时间：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:value="${#dates.format(conference.updateTime, 'yyyy-MM-dd HH:mm:ss')}" readonly>
                </div>
            </div>
            <div class="form-group" th:if="${conference.photoPath != null and conference.photoPath != ''}">
                <label class="col-sm-3 control-label">会议照片：</label>
                <div class="col-sm-8">
                    <img th:src="${@config.getProfile() + conference.photoPath}" style="max-width: 300px; max-height: 200px;" class="img-responsive" alt="会议照片">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
</body>
</html>
